type RootStackParamList = {
    Tabs: NavigatorScreenParams<BottomUserTabsStackParamList>;
    SavedPlaceDetails: {
        placeId: string;
    };
    SavedPlaceDetailsEdit: {
        placeId: string;
        fieldName: FIELD_NAME;
    };
    SavedPlaceAdd: {
        geometry: Geometry;
        placeType: PlaceType;
    };
    UserPersonalInfo: {
        
    };
    UserPersonalInfoEdit: {
        fieldName: string,
        fieldLabel: string,
        userDetails: UserInfo
    };
    Subscription: undefined;
};

type BottomUserTabsStackParamList = {
    ExploreTab: NavigatorScreenParams<HomeStackParamList>;
    SavedPlacesTab: NavigatorScreenParams<SavedPlacesParamList>;
    AccountTab: NavigatorScreenParams<AccountStackParamList>;
};

type AccountStackParamList = {
    Account: undefined;
};

type SavedPlacesParamList = {
    SavedPlaces: undefined;
};

type HomeStackParamList = {
    Home: {
        placeId?: string;
        geometry: Geometry;
        placeName?: string;
        operation?: 'SEARCH' | 'DETAILS';
    } | undefined;
    Search: undefined
};
