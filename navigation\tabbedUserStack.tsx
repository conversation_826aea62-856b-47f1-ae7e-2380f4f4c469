import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createNativeStackNavigator as createStackNavigator } from '@react-navigation/native-stack';
import React, { useContext } from 'react';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import AccountScreen from '../screens/user/account/AccountScreen';
import HomeScreen from "../screens/user/HomeScreen";
import SavedPlacesScreen from '../screens/user/saved-places/SavedPlacesScreen';
import SearchScreen from "../screens/user/SearchScreen";
import { translationServiceContext } from "../services/provider";
import { TranslationService } from "../services/translationService";
import { StackActions, NavigationState, PartialState } from '@react-navigation/native';

const Stack = createStackNavigator();

function HomeStack() {
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const appTitle: string = translationService.translate("APP_TITLE");

    return (
        <Stack.Navigator>
            <Stack.Screen
                name="Home"
                component={HomeScreen}
                options={{
                    title: appTitle,
                    headerShown: true
                }}
            />
            <Stack.Screen
                name="Search"
                component={SearchScreen}
                options={{
                    title: appTitle,
                    headerShown: true,
                    headerBackTitleVisible: false,
                    animation: 'none'
                }}
            />
        </Stack.Navigator>
    )
};

function AccountStack() {
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const accountTabLabel: string = translationService.translate("TABS_ACCOUNT");

    return (
        <Stack.Navigator>
            <Stack.Screen
                name="Account"
                component={AccountScreen}
                options={{
                    title: accountTabLabel,
                    headerShown: true
                }}
            />
        </Stack.Navigator>
    )
};

function SavedPlacesStack() {
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const savedTabLabel = translationService.translate("TABS_SAVED");

    return (
        <Stack.Navigator>
            <Stack.Screen
                name="SavedPlaces"
                options={{
                    title: savedTabLabel
                }}
                component={SavedPlacesScreen}
            />
        </Stack.Navigator>
    )
};

const Tab = createBottomTabNavigator<BottomUserTabsStackParamList>();

export default function TabbedUserStack() {
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    return (
        <Tab.Navigator
            screenOptions={{
                tabBarHideOnKeyboard: true
            }}
        >
            <Tab.Screen
                name="ExploreTab"
                component={HomeStack}
                listeners={({ navigation, route }) => ({
                    tabPress: e => {
                        const state = navigation.getState() as NavigationState;
                        const exploreTabState = state.routes
                            .find((route) => route.name === 'ExploreTab')
                            ?.state as NavigationState | undefined;
                        // If the stack is deeper than index 0, pop to top
                        if (exploreTabState && exploreTabState.index > 0) {
                            navigation.dispatch(StackActions.popToTop());
                        }
                    }
                })}
                options={{
                    headerShown: false,
                    tabBarLabel: translationService.translate("TABS_EXPLORE"),
                    tabBarIcon: ({ color, size }) => (
                        <MaterialCommunityIcons name="navigation-variant" color={color} size={size} />
                    )
                }}
            />
            <Tab.Screen
                name="SavedPlacesTab"
                component={SavedPlacesStack}
                options={{
                    headerShown: false,
                    tabBarLabel: translationService.translate("TABS_SAVED"),
                    tabBarIcon: ({ color, size }) => (
                        <MaterialCommunityIcons name="bookmark" color={color} size={size} />
                    )
                }}
            />
            <Tab.Screen
                name="AccountTab"
                component={AccountStack}
                options={{
                    headerShown: false,
                    tabBarLabel: translationService.translate("TABS_ACCOUNT"),
                    tabBarIcon: ({ color, size }) => (
                        <MaterialCommunityIcons name="account-cog" color={color} size={size} />
                    )
                }}
            />
        </Tab.Navigator>
    );
};
