import { StackScreenProps } from '@react-navigation/stack';
import { Auth, getAuth, User, UserInfo, sendEmailVerification } from 'firebase/auth';
import React, { useContext, useState } from 'react';
import { Alert, StyleSheet, View, ScrollView } from 'react-native';
import { Button, Text, Surface, Card, Snackbar, ActivityIndicator } from 'react-native-paper';
import { authSelectors, signOutUser } from '../../services/authService';
import useGlobalStore from '../../services/globalState';
import { apiServiceContext, translationServiceContext } from '../../services/provider';
import { TranslationService } from '../../services/translationService';
import { NativeStackScreenProps } from '@react-navigation/native-stack';
import { ApiService } from '../../services/api/apiService';
import { UserDetails } from '../../services/api/models/userDetails';
import { MaterialCommunityIcons } from '@expo/vector-icons';

type EmailVerificationScreenProps = NativeStackScreenProps<VerifiedUserStackParamList, 'EmailVerification'>;

const EmailVerificationScreen: React.FC<EmailVerificationScreenProps> = ({ navigation }) => {

    const apiService: ApiService = useContext<ApiService>(apiServiceContext);
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const auth: Auth = getAuth();

    const setFirebaseUser = useGlobalStore((state) => state.setFirebaseUser);
    const updateUserDetails = useGlobalStore((state) => state.update);
    const firebaseUser: User | null = useGlobalStore(authSelectors.firebaseUser);

    const email = firebaseUser?.email || '';

    // State for UI feedback
    const [isChecking, setIsChecking] = useState(false);
    const [isResending, setIsResending] = useState(false);
    const [snackbarVisible, setSnackbarVisible] = useState(false);
    const [snackbarMessage, setSnackbarMessage] = useState('');
    const [snackbarType, setSnackbarType] = useState<'success' | 'error'>('success');

    const fetchAndUpdateUserDetails = async (userUid: string): Promise<void> => {
        try {
            const response = await apiService.getUserDetails(userUid);

            if (!response.ok) {
                const errorText = await response.text();
                console.error("Failed to fetch user details:", response.status, errorText);
                throw new Error(errorText || "Failed to fetch user details");
            }

            const userDetails: UserDetails = await response.json();
            updateUserDetails(userDetails);
        } catch (error) {
            console.error("Error in fetchAndUpdateUserDetails:", error);
            throw error;
        }
    };

    const showSnackbar = (message: string, type: 'success' | 'error' = 'success') => {
        setSnackbarMessage(message);
        setSnackbarType(type);
        setSnackbarVisible(true);
    };

    const reloadUser = async (): Promise<User | null> => {
        await firebaseUser?.reload();
        await firebaseUser?.getIdToken(true);
        const reloadedUser: User | null = auth.currentUser;
        if (reloadedUser?.emailVerified) {
            setFirebaseUser(reloadedUser ? { ...reloadedUser } as User : null);
            fetchAndUpdateUserDetails(reloadedUser.uid);
        }
        return reloadedUser;
    };

    const handleCheckVerification = async () => {
        if (!firebaseUser) return;

        setIsChecking(true);
        try {
            const reloadedUser = await reloadUser();
            if (reloadedUser?.emailVerified) {
                showSnackbar(translationService.translate("EMAIL_VERIFICATION_SUCCESS"), 'success');
                console.log("Email verified! Proceeding...");
                // Navigation will happen automatically due to the auth state change
            } else {
                showSnackbar(translationService.translate("EMAIL_VERIFICATION_STILL_PENDING"), 'error');
                console.log("Email still not verified.");
            }
        } catch (error) {
            console.error("Error during verification check:", error);
            showSnackbar(translationService.translate("EMAIL_VERIFICATION_ERROR"), 'error');
        } finally {
            setIsChecking(false);
        }
    };

    const handleResendEmail = async () => {
        if (!firebaseUser) return;

        setIsResending(true);
        try {
            await sendEmailVerification(firebaseUser);
            showSnackbar(translationService.translate("EMAIL_VERIFICATION_RESEND_SUCCESS"), 'success');
        } catch (error) {
            console.error("Error resending verification email:", error);
            showSnackbar(translationService.translate("EMAIL_VERIFICATION_RESEND_ERROR"), 'error');
        } finally {
            setIsResending(false);
        }
    };

    const handleWrongEmail = () => {
        Alert.alert(
            translationService.translate("EMAIL_VERIFICATION_WRONG_EMAIL_BUTTON"),
            translationService.translate("EMAIL_NOT_VERIFIED_DIALOG_CONTENT"),
            [
                {
                    text: translationService.translate("CANCEL"),
                    style: "cancel"
                },
                {
                    text: translationService.translate("OK"),
                    onPress: () => signOutUser()
                }
            ]
        );
    };

    return (
        <View style={styles.container}>
            <Text variant="titleLarge" style={styles.title}>
                {translationService.translate("EMAIL_VERIFICATION_TITLE") || 'Vérifiez votre email !'}
            </Text>
            <Text style={styles.content}>
                {translationService.translate("EMAIL_VERIFICATION_MESSAGE") ||
                    "Un email de vérification a été envoyé à votre adresse. Veuillez consulter votre boîte de réception pour activer votre compte. Si vous ne voyez pas l’email, vérifiez vos spams ou contactez-nous."}
            </Text>
            <Text style={styles.email}>{email}</Text>
            <Button
                mode="contained"
                style={styles.button}
                onPress={async () => {
                    try {
                        const reloadedUser = await reloadUser();
                        if (reloadedUser?.emailVerified) {
                            console.log("Email verified! Proceeding...");
                            //navigation.navigate('Home');
                        } else {
                            console.log("Email still not verified.");
                            // TODO 
                        }
                    } catch (e) {
                        console.error("Error during verification check:", e);
                        Alert.alert("Error", "We couldn’t refresh your session. Try signing out and back in.");
                    }
                }}
            >
                I’ve verified
            </Button>
            <Button
                mode="outlined"
                style={styles.button}
                onPress={() => { }

                }
            >
                Resend verification email
            </Button>
            <Button
                mode="outlined"
                style={styles.button}
                onPress={() => {
                    signOutUser();
                }}
            >
                Wrong email ?
            </Button>
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: '#fff',
        padding: 24,
    },
    surface: {
        padding: 24,
        borderRadius: 16,
        alignItems: 'center',
        minWidth: 300,
        maxWidth: '90%',
    },
    title: {
        marginBottom: 16,
        textAlign: 'center',
    },
    content: {
        marginBottom: 16,
        textAlign: 'center',
        color: '#444',
    },
    email: {
        marginBottom: 32,
        textAlign: 'center',
        color: '#4682B4',
        fontWeight: 'bold',
        fontSize: 16,
    },
    button: {
        minWidth: 120,
    },
});

export default EmailVerificationScreen;
