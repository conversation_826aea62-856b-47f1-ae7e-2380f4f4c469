import 'dotenv/config';

function getVersionCode(): number {
  return Math.floor(Date.now() / 1000);
}

const versionCode = getVersionCode();

export default {
  expo: {
    owner: "hazysoft",
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    slug: "pharma-chainage",
    version: "1.0.1",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "myapp",
    userInterfaceStyle: "automatic",
    splash: {
      image: "./assets/images/splash.png",
      resizeMode: "contain",
      backgroundColor: "#448c27"
    },
    assetBundlePatterns: [
      "**/*"
    ],
    ios: {
      config: {
        googleMapsApiKey: process.env.GOOGLE_MAPS_IOS_API_KEY
      },
      supportsTablet: true,
      bundleIdentifier: "com.hazysoft.pharmachainage",
      buildNumber: versionCode.toString()
    },
    android: {
      config: {
        googleMaps: {
          apiKey: process.env.GOOGLE_MAPS_ANDROID_API_KEY
        }
      },
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png",
        backgroundColor: "#448c27"
      },
      package: "com.hazysoft.pharmachainage",
      versionCode: versionCode
    },
    experiments: {
      typedRoutes: true
    },
    extra: {
      eas: {
        projectId: "5c953407-3b0b-4438-918c-b08295452d2c"
      }
    },
    runtimeVersion: {
      policy: "appVersion"
    },
    updates: {
      url: "https://u.expo.dev/5c953407-3b0b-4438-918c-b08295452d2c"
    },
    plugins: [
      [
        "expo-secure-store"
      ],
      [
        "expo-location",
        {
          "locationAlwaysAndWhenInUsePermission": "$(PRODUCT_NAME) utilise votre position pour afficher les pharmacies à proximité de votre emplacement actuel.",
          "locationAlwaysPermission": "$(PRODUCT_NAME) utilise votre position pour afficher les pharmacies à proximité de votre emplacement actuel.",
          "locationWhenInUsePermission": "$(PRODUCT_NAME) utilise votre position pour afficher les pharmacies à proximité de votre emplacement actuel."
        }
      ],
      [
        "expo-localization",
      ],
      [
        "expo-build-properties",
        {
          android: {
            minSdkVersion: 24,
            compileSdkVersion: 35,
            targetSdkVersion: 35,
          }
        }
      ]
    ]
  }
};
