import { SavedPlace } from "./savedPlace";
import { Subscription } from "./subscription";

export interface UserDetails extends BaseUser {
    readonly savedPlaces: ReadonlyArray<SavedPlace>;
    readonly subscriptions: ReadonlyArray<Subscription>;
};

export interface BaseUser {
    readonly id: number;
    readonly uid: string;
    readonly uuid: string;
    readonly firstName?: string;
    readonly lastName?: string;
    readonly email: string;
    readonly phoneNumber?: string;
    readonly createdAt: string;
    readonly updatedAt?: string;
    readonly timezone: string;
    readonly trialStartDate?: string;
    readonly trialPeriodInDays?: number;
    readonly whatsappNumber?: string
};
