import AsyncStorage from '@react-native-async-storage/async-storage';
import { addDays, isAfter, isWithinInterval, parseISO } from 'date-fns';
import { User } from 'firebase/auth';
import _, { includes } from 'lodash';
import { Region } from 'react-native-maps';
import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { PlaceType } from '../models/constants';
import { CustomPlace, Place } from '../models/place';
import { SubscriptionPeriod } from '../models/types';
import { UserInfo } from '../models/userDetails';
import { uniqueElements } from '../utils/arrays';
import { INFINITE_DATE, NUMBER_OF_EXISTING_PHARMACIES_MARKERS_TO_KEEP, SEARCH_HISTORY_RETENTION_DAYS, SNACKBAR_DURATION_IN_MILLIS } from '../utils/constants';
import { mapSavedPlaceToCustomPlace, mapSavedPlaceToPlace } from '../utils/others';
import { SavedPlace } from './api/models/savedPlace';
import { Subscription } from './api/models/subscription';
import { UserDetails } from './api/models/userDetails';

interface MultiTenantPersistentState {
    readonly pharmacies: {
        [userUid: string]: ReadonlyArray<Place>
    };
}

interface PersistentState {
    readonly firebaseUser: User | null;
    readonly user: UserInfo | null;
    readonly savedPlaces: ReadonlyArray<CustomPlace>;
    readonly currentRegion: Region;
}

interface NonPersistentState {
    readonly authenticating: boolean;
    readonly checkingPurchase: boolean;
    readonly subscriptionSuccessVisible: boolean;
    readonly operations: ReadonlyArray<{ operationId: string, message: string, type: string, inProgress: boolean }>;
    readonly _hasHydrated: boolean;
}

const MOROCCO_REGION: Region = {
    latitude: 30.712586504549233,
    latitudeDelta: 14.244257967037946,
    longitude: -7.224525790661573,
    longitudeDelta: 10.26430394500494
};

const defaultState: MultiTenantPersistentState & PersistentState & NonPersistentState = {
    firebaseUser: null,
    user: null,
    savedPlaces: [],
    pharmacies: {},
    currentRegion: MOROCCO_REGION,
    authenticating: false,
    checkingPurchase: false,
    subscriptionSuccessVisible: false,
    operations: [],
    _hasHydrated: false
};

export interface GlobalStore extends MultiTenantPersistentState, PersistentState, NonPersistentState {
    // Hydration
    readonly setHasHydrated: (state: boolean) => void;
    // Region
    readonly setCurrentRegion: (region: Region) => void;
    // User
    readonly isUserSubscribed: (currentTime: Date) => boolean;
    readonly findCurrentOrLatestPastPeriod: (currentTime: Date) => SubscriptionPeriod | null;
    readonly findCurrentOrLatestPastPeriodByType: (currentTime: Date, type: 'TRIAL' | 'SUBSCRIPTION') => SubscriptionPeriod | null;
    readonly update: (userDetails: UserDetails | null) => void;
    readonly updateUser: (userDetails: UserDetails) => void;
    readonly clearStateExceptPharmacies: () => Promise<void>;
    // Pharmacies
    readonly addPharmacy: (pharmacy: SavedPlace) => void;
    readonly deletePharmacy: (placeId: string) => void;
    readonly clearPharmacies: (userUid: string) => void;
    readonly addPharmacies: (pharmaciesSearchResult: ReadonlyArray<Place>) => void;
    readonly deleteHiddenExistingPharmacies: () => void;
    // Saved places
    readonly findSavedPlace: (placeId: string) => CustomPlace | undefined;
    readonly findRequiredSavedPlace: (placeId: string) => CustomPlace;
    readonly addSavedPlace: (savedPlace: SavedPlace) => void;
    readonly deleteSavedPlace: (savedPlaceId: string) => void;
    readonly updateSavedPlace: (savedPlace: SavedPlace) => void;
    readonly updateSavedPlaces: (savedPlaces: ReadonlyArray<SavedPlace>) => void;
    // Purchase Flow
    readonly setCheckingPurchase: (isChecking: boolean) => void;
    readonly setSubscriptionSucessVisible: (visible: boolean) => void;
    // Operations
    readonly findOperation: (operationId: string) => boolean;
    readonly startOperation: (operationId: string, message: string, type: string) => void;
    readonly completeOperation: (operationId: string, newMessage: string, newType: string, duration?: number) => void;
    readonly removeOperation: (operationId: string) => void;
    // Snackbar
    readonly showSnackbar: (message: string, type: string, duration?: number) => void;
    // Auth state
    readonly setAuthLoading: (loading: boolean) => void;
}

const filterPharmaciesBasedOnRetention = (pharmacies: ReadonlyArray<Place>): ReadonlyArray<Place> => {
    const now: Date = new Date();
    return pharmacies.map(pharmacy => ({
        ...pharmacy,
        addedAt: pharmacy.addedAt || now
    })).filter(pharmacy => {
        if (pharmacy.type !== PlaceType.EXISTING_PHARMACY) {
            return true;
        }
        const addedAt: Date = pharmacy.addedAt ?? now;
        return isAfter(addDays(addedAt, SEARCH_HISTORY_RETENTION_DAYS), now);
    });
};

const useGlobalStore = create<GlobalStore>()(devtools(persist(
    (set, get) => ({
        ...defaultState,
        setHasHydrated: (state: boolean) => {
            if (state) {
                Object.keys(get().pharmacies).forEach((userUid: string) => {
                    const userPharmacies: ReadonlyArray<Place> = get().pharmacies[userUid] ?? [];
                    const userFilteredPharmacies: ReadonlyArray<Place> = filterPharmaciesBasedOnRetention(userPharmacies);
                    set({
                        pharmacies: {
                            ...get().pharmacies,
                            [userUid]: userFilteredPharmacies
                        }
                    });
                });
                set({
                    _hasHydrated: state
                });
            } else {
                set({ _hasHydrated: state });
            }
        },
        setAuthLoading: (loading: boolean) => set({ authenticating: loading }),

        // Methods
        setCurrentRegion: (region: Region) => set({ currentRegion: region }),
        update: (userDetails: UserDetails | null) => {
            if (!userDetails) {
                set({
                    user: null,
                    savedPlaces: []
                });
                return;
            }
            set({
                user: {
                    id: userDetails.id,
                    uid: userDetails.uid,
                    uuid: userDetails.uuid,
                    firstName: userDetails.firstName,
                    lastName: userDetails.lastName,
                    email: userDetails.email,
                    phoneNumber: userDetails.phoneNumber,
                    createdAt: userDetails.createdAt,
                    updatedAt: userDetails.updatedAt,
                    timezone: userDetails.timezone,
                    trialStartDate: userDetails.trialStartDate,
                    trialPeriodInDays: userDetails.trialPeriodInDays,
                    subscriptions: userDetails.subscriptions.map((sub: Subscription) => ({
                        ...sub,
                        plan: sub.plan
                    })),
                    whatsappNumber: userDetails.whatsappNumber
                },
                savedPlaces: userDetails.savedPlaces.map(mapSavedPlaceToCustomPlace)
            })
        },
        updateUser: (userDetails: UserDetails) => set({
            // todo update not used / remove unupdatable fields
            user: {
                id: userDetails.id,
                uid: userDetails.uid,
                uuid: userDetails.uuid,
                firstName: userDetails.firstName,
                lastName: userDetails.lastName,
                email: userDetails.email,
                phoneNumber: userDetails.phoneNumber,
                createdAt: userDetails.createdAt,
                updatedAt: userDetails.updatedAt,
                timezone: userDetails.timezone,
                trialStartDate: userDetails.trialStartDate,
                trialPeriodInDays: userDetails.trialPeriodInDays,
                subscriptions: userDetails.subscriptions.map((sub: Subscription) => ({
                    ...sub,
                    plan: sub.plan
                })), whatsappNumber: userDetails.whatsappNumber
            }
        }),
        findSavedPlace: (placeId: string): CustomPlace | undefined =>
            get().savedPlaces.find((savedPlace: CustomPlace) => placeId === savedPlace.placeId),
        findRequiredSavedPlace: (placeId: string): CustomPlace => {
            const savedPlace: CustomPlace | undefined = get().findSavedPlace(placeId);
            if (!savedPlace) {
                throw Error(`Can't find saved place (id=${placeId})`)
            } else {
                return savedPlace;
            }
        },
        deletePharmacy: (placeId: string) => {
            const currentUserUid: string | undefined = get().user?.uid;
            if (!currentUserUid) return;
            const currentUserPharmacies: ReadonlyArray<Place> = get().pharmacies[currentUserUid] ?? [];
            set({
                pharmacies: {
                    ...get().pharmacies,
                    [currentUserUid]: _.filter(currentUserPharmacies, (pharmacy) => placeId !== pharmacy.placeId)
                }
            })
        },
        clearPharmacies: (userUid: string) => {
            set({
                pharmacies: {
                    ...get().pharmacies,
                    [userUid]: []
                }
            })
        },
        addPharmacies: (pharmaciesSearchResult: ReadonlyArray<Place>) => {
            const currentUserUid: string | undefined = get().user?.uid;
            if (!currentUserUid) return;
            const currentUserPharmacies: ReadonlyArray<Place> = get().pharmacies[currentUserUid] ?? [];
            const mappedPharmaciesSearchResult: ReadonlyArray<Place> = pharmaciesSearchResult.map((pharmacy: Place) => ({
                ...pharmacy,
                type: PlaceType.EXISTING_PHARMACY,
                addedAt: new Date()
            } as Place));
            const currentUserMergedPharmacies: ReadonlyArray<Place> = uniqueElements(
                [...mappedPharmaciesSearchResult, ...currentUserPharmacies].slice(-NUMBER_OF_EXISTING_PHARMACIES_MARKERS_TO_KEEP),
                (pharmacy) => pharmacy.placeId
            );
            return set({
                pharmacies: {
                    ...get().pharmacies,
                    [currentUserUid]: _.differenceBy(currentUserMergedPharmacies, get().savedPlaces, 'placeId')
                }
            })
        },
        updateSavedPlaces: (savedPlaces: ReadonlyArray<SavedPlace>) => set({
            savedPlaces: savedPlaces.map(mapSavedPlaceToCustomPlace)
        }),
        updateSavedPlace: (savedPlace: SavedPlace) => set({
            savedPlaces: get().savedPlaces.map(sp => {
                if (savedPlace.placeId === sp.placeId) {
                    return mapSavedPlaceToCustomPlace(savedPlace);
                } else {
                    return sp;
                }
            })
        }),
        addPharmacy: (savedPlace: SavedPlace) => {
            const currentUserUid: string | undefined = get().user?.uid;
            if (!currentUserUid) return;
            const currentUserPharmacies: ReadonlyArray<Place> = get().pharmacies[currentUserUid] ?? [];
            set({
                pharmacies: {
                    ...get().pharmacies,
                    [currentUserUid]: [mapSavedPlaceToPlace(savedPlace), ...currentUserPharmacies]
                }
            })
        },
        addSavedPlace: (savedPlace: SavedPlace) => set((store) => ({
            savedPlaces: [mapSavedPlaceToCustomPlace(savedPlace), ...store.savedPlaces]
        })),
        deleteSavedPlace: (savedPlaceId: string) => set((store) => ({
            savedPlaces: store.savedPlaces.filter(savedPlace => savedPlace.placeId !== savedPlaceId)
        })),
        deleteHiddenExistingPharmacies: () => set({
            savedPlaces: _.filter(get().savedPlaces, (savedPlace) => PlaceType.EXISTING_PHARMACY !== savedPlace.type && !includes(savedPlace.tags, 'HIDDEN'))
        }),
        // ========= OPERATIONS =========
        findOperation: (operationId: string): boolean =>
            _.some(get().operations, { operationId }),
        startOperation: (operationId, message, type) => set((state) => ({
            operations: [
                ...state.operations,
                { operationId, message, type, inProgress: true },
            ],
        })),
        completeOperation: (operationId, newMessage, newType, duration = SNACKBAR_DURATION_IN_MILLIS) => {
            set((state) => {
                const updatedOperations = state.operations.map((op) =>
                    op.operationId === operationId ? { ...op, message: newMessage, type: newType } : op
                );

                return { operations: updatedOperations };
            });

            // Keep the snackbar visible for the duration, then set `inProgress` to false
            setTimeout(() => {
                set((state) => {
                    const updatedOperations = state.operations.map((op) =>
                        op.operationId === operationId ? { ...op, inProgress: false } : op
                    );

                    return { operations: updatedOperations };
                });
            }, duration - 1000);  // Subtract a small buffer before hiding the snackbar

            // Then remove the operation from the list after the full duration
            setTimeout(() => {
                set((state) => ({
                    operations: state.operations.filter((op) => op.operationId !== operationId),
                }));
            }, duration);
        },
        removeOperation: (operationId) => set((state) => ({
            operations: state.operations.filter((op) => op.operationId !== operationId),
        })),
        showSnackbar: (message: string, type: string, duration = SNACKBAR_DURATION_IN_MILLIS) => {
            const operationId = _.uniqueId('snackbar_');  // Create a unique operation ID for this snackbar

            // Start the operation to show the snackbar
            set((state) => ({
                operations: [
                    ...state.operations,
                    { operationId, message, type, inProgress: true },
                ],
            }));

            // Automatically remove the snackbar after the specified duration
            setTimeout(() => {
                set((state) => ({
                    operations: state.operations.filter((op) => op.operationId !== operationId),
                }));
            }, duration);
        },
        isUserSubscribed: (currentTime: Date): boolean => {
            const period: SubscriptionPeriod | null = get().findCurrentOrLatestPastPeriod(currentTime);
            if (!period) return false;
            return period.type === 'SUBSCRIPTION' && isWithinInterval(currentTime, { start: period.startDate, end: period.endDate });
        },
        findCurrentOrLatestPastPeriod: (currentTime: Date): SubscriptionPeriod | null => {
            const user: UserInfo | null = get().user;
            if (!user) return null;

            const findActiveSubscription = (): SubscriptionPeriod | null => {
                if (!user.subscriptions?.length) return null;

                const currentSubscription = user.subscriptions.find(sub => {
                    const startDate = parseISO(sub.startDate);
                    const endDate = sub.endDate ? parseISO(sub.endDate) : INFINITE_DATE;
                    return isWithinInterval(currentTime, { start: startDate, end: endDate });
                });

                if (!currentSubscription) return null;

                return {
                    startDate: parseISO(currentSubscription.startDate),
                    endDate: currentSubscription.endDate ? parseISO(currentSubscription.endDate) : INFINITE_DATE,
                    type: 'SUBSCRIPTION',
                    isPaid: currentSubscription.paid,
                    plan: currentSubscription.plan
                };
            };

            const findLatestPastSubscription = (): SubscriptionPeriod | null => {
                if (!user.subscriptions?.length) return null;

                const latestPastSubscription = _(user.subscriptions)
                    .filter(sub => {
                        const endDate = sub.endDate ? parseISO(sub.endDate) : INFINITE_DATE;
                        return endDate < currentTime;
                    })
                    .maxBy(sub => sub.endDate ? parseISO(sub.endDate) : INFINITE_DATE);

                if (!latestPastSubscription) return null;

                return {
                    startDate: parseISO(latestPastSubscription.startDate),
                    endDate: parseISO(latestPastSubscription.endDate!),
                    type: 'SUBSCRIPTION',
                    isPaid: latestPastSubscription.paid,
                    plan: latestPastSubscription.plan
                };
            };

            const findTrialPeriod = (): SubscriptionPeriod | null => {
                if (!user.trialStartDate || !user.trialPeriodInDays) return null;

                const trialStartDate = parseISO(user.trialStartDate);
                const trialEndDate = addDays(trialStartDate, user.trialPeriodInDays);

                return {
                    startDate: trialStartDate,
                    endDate: trialEndDate,
                    type: 'TRIAL',
                    plan: undefined
                };
            };

            return findActiveSubscription() || findLatestPastSubscription() || findTrialPeriod() || null;
        },
        findCurrentOrLatestPastPeriodByType: (currentTime: Date, type: 'TRIAL' | 'SUBSCRIPTION'): SubscriptionPeriod | null => {
            const period: SubscriptionPeriod | null = get().findCurrentOrLatestPastPeriod(currentTime);
            if (!period) return null;
            return period.type === type ? period : null;
        },
        setCheckingPurchase: (checking: boolean) => set({ checkingPurchase: checking }),
        setSubscriptionSucessVisible: (visible: boolean) => set({ subscriptionSuccessVisible: visible }),
        clearStateExceptPharmacies: async () => {
            set({
                firebaseUser: null,
                user: null,
                savedPlaces: [],
                currentRegion: MOROCCO_REGION,
                authenticating: false,
                checkingPurchase: false,
                subscriptionSuccessVisible: false,
                operations: []
            });
        },
    }),
    {
        name: 'global-state',
        storage: createJSONStorage(() => AsyncStorage),
        // Only persist specific keys
        partialize: (state) => ({
            user: state.user,
            savedPlaces: state.savedPlaces,
            pharmacies: state.pharmacies,
            currentRegion: state.currentRegion
        }),
        // Add onRehydrateStorage callback
        onRehydrateStorage: () => (state) => {
            if (state) {
                state.setHasHydrated(true);
            }
        },
    }
)));

export default useGlobalStore;
