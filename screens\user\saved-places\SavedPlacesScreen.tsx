import { BottomTabScreenProps } from "@react-navigation/bottom-tabs";
import { CompositeScreenProps } from "@react-navigation/native";
import { NativeStackScreenProps } from "@react-navigation/native-stack";
import React, { useContext, useState } from "react";
import { RefreshControl, ScrollView, StyleSheet, View } from 'react-native';
import { Divider, Icon, IconButton, List, Text } from "react-native-paper";
import { PlaceType, PlaceTypeParams, placeTypeConf } from "../../../models/constants";
import { CustomPlace } from '../../../models/place';
import { ApiService } from "../../../services/api/apiService";
import { UserDetails } from "../../../services/api/models/userDetails";
import useGlobalStore from '../../../services/globalState';
import { apiServiceContext, translationServiceContext } from "../../../services/provider";
import { TranslationService } from '../../../services/translationService';
import { authSelectors } from "../../../services/authService";

type SavedPlacesScreenProps = CompositeScreenProps<
    BottomTabScreenProps<SavedPlacesParamList, 'SavedPlaces'>,
    NativeStackScreenProps<RootStackParamList>
>;

const SavedPlacesScreen: React.FC<SavedPlacesScreenProps> = ({ navigation }) => {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);
    const apiService: ApiService = useContext<ApiService>(apiServiceContext);

    const userUid: string = useGlobalStore(authSelectors.authenticatedUserId);

    const savedPlaces: ReadonlyArray<CustomPlace> = useGlobalStore((store) => store.savedPlaces.filter(savedPlace => !(savedPlace.type === PlaceType.EXISTING_PHARMACY)));
    const update = useGlobalStore((store) => store.update);
    const showSnackbar = useGlobalStore((state) => state.showSnackbar);

    const [refreshing, setRefreshing] = useState<boolean>(false);

    const onItemPress = (savedPlace: CustomPlace) => {
        navigation.navigate({
            name: "SavedPlaceDetails",
            params: {
                placeId: savedPlace.placeId
            }
        });
    };

    const refreshUserData = async (): Promise<void> => {
        setRefreshing(true);
        try {
            const response = await apiService.getUserDetails(userUid);
            if (response.ok) {
                const userDetails: UserDetails = await response.json();
                update(userDetails);
            } else {
                throw new Error("Failed to fetch user details");
            }
        } catch (error) {
            console.error("Failed to refresh user data:", error);
            showSnackbar(translationService.translate("FAILED_TO_REFRESH_USER_DATA"), "error");
            throw error; // Propagate the error to the caller
        } finally {
            setRefreshing(false);
        }
    };

    return (
        <ScrollView
            refreshControl={
                <RefreshControl refreshing={refreshing} onRefresh={refreshUserData} />
            }
        >
            {savedPlaces?.length < 1 && (
                <View style={styles.emptyListContainer}>
                    <Icon source="bookmark-off-outline" size={100} />
                    <Text variant="bodyLarge" style={styles.textAlignCenter}>
                        Aucun chaînage ou pharmacie n'est enregistré.</Text>
                    <Text variant="bodyMedium" style={styles.textAlignCenter}>
                        Pour en ajouter, allez dans l'onglet <Text style={styles.boldFontWeigth}>"Explorer"</Text> et cliquez sur le bouton d'ajout.
                    </Text>
                </View>
            )}
            {savedPlaces.map(savedPlace => {
                const placeTypeParams: PlaceTypeParams = placeTypeConf[savedPlace.type];
                return (
                    <View key={savedPlace.placeId}>
                        <List.Item
                            style={styles.listItem}
                            onPress={() => onItemPress(savedPlace)}
                            title={<Text variant="titleMedium">{savedPlace.name}</Text>}
                            description={({ selectable, ellipsizeMode, color, fontSize }) => (
                                <View style={styles.descriptionView}>
                                    {savedPlace.formattedAddress ? (<Text numberOfLines={2}>{savedPlace.formattedAddress}</Text>) : null}
                                    <Text style={{ color: placeTypeParams.style.color, ...styles.placeType }}>
                                        {placeTypeParams.label}
                                    </Text>
                                </View>
                            )}
                            right={(props) => (
                                <IconButton
                                    icon="chevron-right"
                                    iconColor={props.color}
                                    style={props.style}
                                    onPress={() => {
                                        onItemPress(savedPlace);
                                    }}
                                />
                            )}
                        />
                        <Divider />
                    </View>
                )
            })}
        </ScrollView>
    );
};


const styles = StyleSheet.create({
    textAlignCenter: {
        textAlign: 'center'
    },
    emptyListContainer: {
        marginTop: 24,
        padding: 24,
        flex: 1, // Ensures the container takes up the entire screen
        justifyContent: 'center', // Centers vertically
        alignItems: 'center', // Centers horizontally
        opacity: 0.7,
        gap: 16
    },
    boldFontWeigth: {
        fontWeight: "bold"
    },
    listItem: {
        borderRadius: 0
    },
    descriptionView: {
        flex: 1,
        gap: 8
    },
    placeType: {
        fontWeight: 'bold'
    }
});

export default SavedPlacesScreen;
