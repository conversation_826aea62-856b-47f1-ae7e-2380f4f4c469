import { NativeStackScreenProps } from "@react-navigation/native-stack";
import React, { useContext } from "react";
import { ScrollView, StyleSheet, View } from 'react-native';
import { Divider } from "react-native-paper";
import ReadonlyInfo from "../../../../components/ReadonlyInfo";
import { UserInfo } from "../../../../models/userDetails";
import { authSelectors } from "../../../../services/authService";
import useGlobalStore from '../../../../services/globalState';
import { translationServiceContext } from "../../../../services/provider";
import { TranslationService } from "../../../../services/translationService";

type UserPersonalInfoScreenProps = NativeStackScreenProps<RootStackParamList, 'UserPersonalInfo'>;

const UserPersonalInfoScreen: React.FC<UserPersonalInfoScreenProps> = ({ navigation, route }) => {

    const userDetails: UserInfo | undefined = useGlobalStore(authSelectors.authenticatedUserDetails);

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const nameLabel = translationService.translate("SIGN_UP_FORM_NAME");
    const emailLabel = translationService.translate("FIELD_EMAIL");
    const whatsappNumberLabel = translationService.translate("FIELD_WHATSAPP_NUMBER");

    const navigateToEditModal = (userDetails: UserInfo, fieldName: string, fieldLabel: string) => {
        navigation.navigate({
            name: "UserPersonalInfoEdit",
            params: {
                fieldName,
                fieldLabel,
                userDetails,
            }
        });
    };

    return (
        <ScrollView>
            <View>
                <View style={styles.formContainer}>
                    <ReadonlyInfo
                        label={nameLabel}
                        value={userDetails.firstName}
                        editDisabled={false}
                        onEditPress={() => navigateToEditModal(userDetails, "name", nameLabel)}
                    />
                    <Divider />
                    <ReadonlyInfo
                        label={emailLabel}
                        value={userDetails.email}
                        editDisabled={true}
                    />
                    <Divider />
                    <ReadonlyInfo
                        label={whatsappNumberLabel}
                        value={userDetails.whatsappNumber}
                        editDisabled={false}
                        onEditPress={() => navigateToEditModal(userDetails, "whatsappNumber", whatsappNumberLabel)}
                    />
                    <Divider />
                </View>
            </View>
        </ScrollView>
    );
};

const styles = StyleSheet.create({
    formContainer: {
        flexDirection: 'column',
        gap: 8,
        padding: 16,
    },
    descriptionView: {
        flex: 1
    },
    placeType: {
        fontWeight: 'bold'
    }
});

export default UserPersonalInfoScreen;
