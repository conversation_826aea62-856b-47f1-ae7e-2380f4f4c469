import { createNativeStackNavigator as createStackNavigator } from '@react-navigation/native-stack';
import React, { useContext } from 'react';
import EditHeaderRight from '../components/EditHeaderRigth';
import SavedPlaceDetailsHeaderRight from '../components/SavedPlaceDetailsHeaderRigth';
import SavedPlaceAddModal from '../screens/user/saved-places/SavedPlaceAddModal';
import SavedPlaceDetailsEditModal from '../screens/user/saved-places/SavedPlaceDetailsEditModal';
import SavedPlaceDetailsScreen from '../screens/user/saved-places/SavedPlaceDetailsScreen';
import UserPersonalInfoEditModal from '../screens/user/account/profile/UserPersonalInfoEditModal';
import UserPersonalInfoScreen from '../screens/user/account/profile/UserPersonalInfoScreen';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';
import TabbedUserStack from './tabbedUserStack';
import SubscriptionScreen from '../screens/user/account/subscription/SubscriptionScreen';

const Stack = createStackNavigator<RootStackParamList>();

export default function UserStack() {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    return (
        <Stack.Navigator>
            <Stack.Group>
                <Stack.Screen
                    name="Tabs"
                    component={TabbedUserStack}
                    options={{
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name="SavedPlaceDetails"
                    component={SavedPlaceDetailsScreen}
                    options={({ route }) => ({
                        title: route.params?.placeId,
                        headerBackTitleVisible: false,
                        headerRight: () => (<SavedPlaceDetailsHeaderRight />)
                    })}
                />
                <Stack.Screen
                    name="UserPersonalInfo"
                    component={UserPersonalInfoScreen}
                    options={({ route }) => ({
                        title: translationService.translate("PERSONAL_INFO"),
                        headerBackTitleVisible: true,
                        headerBackTitle: translationService.translate("TABS_ACCOUNT")
                    })}
                />
                <Stack.Screen
                    name="Subscription"
                    component={SubscriptionScreen}
                    options={({ navigation }) => ({
                        title: translationService.translate("SUBSCRIPTION_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackTitle: translationService.translate("TABS_ACCOUNT")
                    })}
                />
            </Stack.Group>
            <Stack.Group screenOptions={{ presentation: 'modal', headerBackTitleVisible: true, headerBackButtonMenuEnabled: true }}>
                <Stack.Screen
                    name="SavedPlaceDetailsEdit"
                    component={SavedPlaceDetailsEditModal}
                    options={{
                        headerTitleAlign: "center",
                        headerBackButtonMenuEnabled: true,
                        headerBackVisible: true,
                        headerRight: () => (<EditHeaderRight />)
                    }}
                />
                <Stack.Screen
                    name="SavedPlaceAdd"
                    component={SavedPlaceAddModal}
                    options={{
                        headerTitleAlign: "center",
                        headerBackButtonMenuEnabled: true,
                        headerBackVisible: true,
                        headerRight: () => (<EditHeaderRight />)
                    }}
                />
                <Stack.Screen
                    name="UserPersonalInfoEdit"
                    component={UserPersonalInfoEditModal}
                    options={{
                        headerTitleAlign: "center",
                        headerBackButtonMenuEnabled: true,
                        headerBackVisible: true,
                        headerRight: () => (<EditHeaderRight />)
                    }}
                />
            </Stack.Group>
        </Stack.Navigator>
    );
}
