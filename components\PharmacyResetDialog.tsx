import * as React from 'react';
import { StyleSheet, View } from "react-native";
import { Button, Dialog, Portal, PaperProvider, Text, Checkbox } from 'react-native-paper';
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';
interface Props {
    readonly visible: boolean;
    readonly onConfirm: (removeExistingPharmaciesWithHiddenCircles: boolean) => Promise<void>;
    readonly onHide: () => void;
}

const PharmacyResetDialog = (props: Props) => {

    const translationService: TranslationService = React.useContext<TranslationService>(translationServiceContext);
    const [confirming, setConfirming] = React.useState<boolean>(false);
    const [removeExistingPharmaciesWithHiddenCircles, setRemoveExistingPharmaciesWithHiddenCircles] = React.useState(true);

    return (
        <Portal>
            <Dialog visible={props.visible} onDismiss={props.onHide}>
                <Dialog.Title>{translationService.translate("PHARMACY_RESET_DIALOG_TITLE")}</Dialog.Title>
                <Dialog.Content style={styles.content}>
                    <Text variant="bodyLarge">{translationService.translate("PHARMACY_REST_DIALOG_CONTENT")}</Text>
                    <View style={styles.checkboxContainer}>
                        <Checkbox.Android
                            status={removeExistingPharmaciesWithHiddenCircles ? 'checked' : 'unchecked'}
                            onPress={() => {
                                setRemoveExistingPharmaciesWithHiddenCircles(!removeExistingPharmaciesWithHiddenCircles);
                            }}
                        />
                        <Text
                            onPress={() => setRemoveExistingPharmaciesWithHiddenCircles(!removeExistingPharmaciesWithHiddenCircles)}
                            variant="bodyMedium"
                            style={styles.checkboxText}
                        >
                            {translationService.translate("PHARMACY_RESET_DIALOG_CHECKBOX")}
                        </Text>
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button
                        onPress={props.onHide}
                    >
                        {translationService.translate("CANCEL")}
                    </Button>
                    <Button
                        onPress={() => {
                            setConfirming(true);
                            props.onConfirm(removeExistingPharmaciesWithHiddenCircles)
                                .finally(() => {
                                    setConfirming(false);
                                })
                        }}
                        loading={confirming}
                    >
                        {translationService.translate("ERASE")}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

const styles = StyleSheet.create({
    content: {
        gap: 8
    },
    checkboxContainer: {
        flexDirection: 'row',
        alignItems: 'center',
        flexWrap: 'wrap'
    },
    checkboxText: {
        flex: 1,
        flexWrap: 'wrap'
    }
});


export default PharmacyResetDialog;