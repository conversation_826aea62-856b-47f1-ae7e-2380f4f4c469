import { createNativeStackNavigator as createStackNavigator } from '@react-navigation/native-stack';
import React, { useContext } from 'react';
import { Platform } from 'react-native';
import AuthFormScreen from '../screens/auth/AuthFormScreen';
import AuthScreen from "../screens/auth/AuthScreen";
import { translationServiceContext } from '../services/provider';
import { TranslationService } from '../services/translationService';
import AuthPasswordResetScreen from '../screens/auth/AuthPasswordResetScreen';
import AuthSignUpFormScreen from '../screens/auth/AuthSignUpFormScreen';

const Stack = createStackNavigator();

export default function AuthStack() {

    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);

    const appTitle: string = translationService.translate("APP_TITLE");

    return (
        <Stack.Navigator initialRouteName="Auth">
            <Stack.Group>
                <Stack.Screen
                    name="Auth"
                    component={AuthScreen}
                    options={{
                        title: appTitle,
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name="AuthForm"
                    component={AuthFormScreen}
                    options={{
                        title: Platform.OS === "ios" ? "" : appTitle,
                        headerBackTitleVisible: true,
                    }}
                />
                <Stack.Screen
                    name="AuthSignUpForm"
                    component={AuthSignUpFormScreen}
                    options={{
                        title: translationService.translate("SIGN_UP_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackButtonMenuEnabled: true
                    }}
                />
                <Stack.Screen
                    name="AuthPasswordReset"
                    component={AuthPasswordResetScreen}
                    options={{
                        title: translationService.translate("RESET_PASSWORD_SCREEN_TITLE"),
                        headerBackTitleVisible: true,
                        headerBackButtonMenuEnabled: true
                    }}
                />
            </Stack.Group>
            {/* <Stack.Group screenOptions={{ presentation: 'modal', headerBackTitleVisible: true, headerBackButtonMenuEnabled: true }}>
            </Stack.Group> */}
        </Stack.Navigator>
    );
}
