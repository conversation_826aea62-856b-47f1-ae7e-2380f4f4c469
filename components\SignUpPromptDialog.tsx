import React from 'react';
import { Platform, View } from 'react-native';
import { Button, Dialog, Text, Paragraph, Portal, useTheme } from 'react-native-paper';
import { useNavigation } from '@react-navigation/native'; // For navigation
import { NativeStackNavigationProp } from '@react-navigation/native-stack'; // For typing navigation
// Assuming you have a RootStackParamList that includes your Auth/SignUp screens
import { translationServiceContext } from "../services/provider"; // If you have translations
import { useContext } from 'react';
import { TranslationService } from '../services/translationService';

type SignUpPromptDialogProps = {
    visible: boolean;
    onDismiss: () => void;
    // Optional: if you want to specify a feature name in the dialog
    featureName?: string;
};


const SignUpPromptDialog: React.FC<SignUpPromptDialogProps> = ({ visible, onDismiss, featureName }) => {
    const theme = useTheme();
    const navigation = useNavigation<NativeStackNavigationProp<any>>(); // For navigation
    const translationService: TranslationService = useContext<TranslationService>(translationServiceContext);


    const handleSignUp = () => {
        onDismiss(); // Close the dialog
        // Navigate to your Sign Up / Login screen
        // This depends on your navigation setup. Example:
        navigation.navigate("AuthSignUpForm"); // Or 'SignUp'
    };

    const title = "Débloquez les fonctionnalités complètes !";
    const message = "Pour ajouter et gérer vos chaînages de pharmacies, créez votre compte gratuit. Vous pourrez aussi profiter de :";
    const signUpButtonText = translationService.translate("SIGN_UP_LOGIN_BUTTON");
    const cancelButtonText = "Pas maintenant";


    return (
        <Portal>
            <Dialog visible={visible} onDismiss={onDismiss}>
                <Dialog.Icon icon="lock-open-variant" size={48} color={theme.colors.primary} />
                <Dialog.Title>{title}</Dialog.Title>
                <Dialog.Content>
                    <Paragraph>
                        Créer un compte gratuit pour:
                    </Paragraph>
                    <View style={{ marginTop: 4 }}>
                        <Paragraph style={{ marginBottom: 4 }}>✅ 7 jours d'essai complet</Paragraph>
                        <Paragraph style={{ marginBottom: 4 }}>📈 Jusqu’à 150 recherches/jour (en s’abonnant)</Paragraph>
                        <Paragraph style={{ marginBottom: 4 }}>📍 Ajouter des pharmacies manquantes</Paragraph>
                        <Paragraph style={{ marginBottom: 0 }}>💾 Sauvegarder vos chaînages</Paragraph>
                    </View>
                </Dialog.Content>
                <Dialog.Actions>
                    <Button
                        onPress={onDismiss}
                        textColor={theme.colors.primary}
                    >
                        {cancelButtonText}
                    </Button>
                    <Button
                        mode="contained"
                        onPress={handleSignUp}
                    >
                        {signUpButtonText}
                    </Button>
                </Dialog.Actions>
            </Dialog>
        </Portal>
    );
};

export default SignUpPromptDialog;